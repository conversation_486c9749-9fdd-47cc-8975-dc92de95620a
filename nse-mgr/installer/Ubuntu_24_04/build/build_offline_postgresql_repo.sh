#!/bin/bash

set -euo pipefail

log() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*"; }
error_exit() { log "❌ 错误: $*"; exit 1; }

TEMP_DIR="/tmp/pg-offline-repo"
OUTPUT_TAR="postgres-offline-repo.tar.bz2"

# =============== 创建临时目录 ===============
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"
cd "$TEMP_DIR"

# =============== 添加 PGDG 源 ===============
log "配置 PostgreSQL 官方源..."

KEYRING="/usr/share/keyrings/postgresql-archive-keyring.gpg"
curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo gpg --dearmor -o "$KEYRING"
echo "deb [signed-by=$KEYRING] http://apt.postgresql.org/pub/repos/apt jammy-pgdg main" | \
    sudo tee /etc/apt/sources.list.d/pgdg.list > /dev/null

sudo apt update || error_exit "APT 更新失败"

# =============== 获取真实下载链接并下载 ===============
log "获取 PostgreSQL 16 下载链接..."

# 生成下载 URL 列表
mapfile -t URIS < <(apt download --print-uris \
    postgresql-16 \
    postgresql-client-16 \
    postgresql-contrib-16 \
    libpq5 \
    postgresql-plperl-16 \
    postgresql-plpython3-16 2>/dev/null | \
    grep -oE "https?://[^']+" )

if [ ${#URIS[@]} -eq 0 ]; then
    error_exit "无法获取下载链接，请检查网络或包名"
fi

log "开始下载 ${#URIS[@]} 个包..."

for uri in "${URIS[@]}"; do
    filename="${uri##*/}"
    log "📥 下载: $filename"
    if ! wget -q "$uri" -O "$filename"; then
        log "❌ 下载失败: $uri"
    else
        log "✅ 成功: $filename"
    fi
done

# =============== 验证下载 ===============
DEB_COUNT=$(find . -name "*.deb" | wc -l)
if [ "$DEB_COUNT" -lt 3 ]; then
    error_exit "下载的 .deb 包过少，可能失败"
fi
log "✅ 共成功下载 $DEB_COUNT 个包"

# =============== 生成 APT 元数据 ===============
log "生成 APT 仓库元数据..."

dpkg-scanpackages . /dev/null | gzip -9c > Packages.gz
dpkg-scanpackages . /dev/null > Packages

cat > Release << EOF
Origin: PostgreSQL Offline
Label: PostgreSQL 16 Repo
Suite: stable
Version: 1.0
Codename: jammy
Architectures: amd64
Components: main
Description: PostgreSQL 16 Offline Repository
Date: $(date -Ru)
EOF

# =============== 打包 ===============
cd /tmp
tar -cjf "$OUTPUT_TAR" -C "$(basename "$TEMP_DIR")" .

log "✅ 离线仓库已生成: $(pwd)/$OUTPUT_TAR"