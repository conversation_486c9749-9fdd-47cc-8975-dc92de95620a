#!/bin/bash

# ===================================================================
# NSE 部署全局配置 (Ubuntu 22.04 LTS 兼容版本)
# ===================================================================

# 脚本根目录
ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
#ROOT_DIR=/data/nse/installer

SCRIPTS_DIR="$ROOT_DIR"
SOFTS_DIR="$ROOT_DIR/../softs"
PROJECTS_DIR="$ROOT_DIR/../../dist"
LOG_FILE="$ROOT_DIR/../logs/nse-deploy.log"

NSE_PROJECT_DIR="/data/ruijie/nse"
NSE_PROJECT_NAME="nse-mgr-launcher-1.0.0-SNAPSHOT.jar"
NSE_MGR_NAME="nse-mgr-1.0.0-SNAPSHOT.jar"

# 数据库配置
DEFAULT_DB_USER="sys_nse_rw"
DEFAULT_DB_PASS="Ruijie@NSE20250902!!!"
DB_NAME="nse"
SCHEMA_NAME="public"
DB_PORT=5432
NSE_MGR_PORT=8080

# NSE 动态配置环境变量键名
CPU_CORES_KEY="NSE_INIT_CPU_CORES"
MAX_MEMORY_KEY="NSE_INIT_MAX_MEMORY"
MAX_DISK_KEY="NSE_INIT_MAX_DISK"
CONCURRENCY_NODES_KEY="NSE_INIT_CONCURRENCY_NODES"

# 部署状态文件
DEPLOYMENT_STATE_FILE=$NSE_PROJECT_DIR"/deploy/nse-deploy-state"
sudo mkdir -p "$ROOT_DIR/../logs" $NSE_PROJECT_DIR"/deploy"

# 工具函数（所有脚本共享）
log() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo "$message"
    echo "$message" >> "$LOG_FILE"
}

error_exit() {
    log "❌ 错误: $1"
    exit 1
}

save_state() {
    echo "$1" >> "$DEPLOYMENT_STATE_FILE"
}
is_state_saved() {
    grep -q "^$1$" "$DEPLOYMENT_STATE_FILE" 2>/dev/null
}

wait_for_service() {
    local service_name=$1
    for i in {1..30}; do
        if systemctl is-active --quiet "$service_name"; then
            log "服务 $service_name 已启动"
            return 0
        fi
        log "等待服务 $service_name 启动... ($i/30)"
        sleep 5
    done
    error_exit "服务 $service_name 启动超时"
}

# 检查端口占用
check_port() {
    local port="$1"
    local expected_process="$2"

    # ✅ 1. 校验端口
    if [ -z "$port" ] || ! [[ "$port" =~ ^[0-9]+$ ]]; then
        error_exit "无效的端口号: '$port'，必须是一个正整数"
    fi

    log "🔍 正在检查端口 $port 是否被占用..."

    # ✅ 2. 使用 ss -tulpn 获取带进程信息的连接（需要 sudo）
    local ss_output
    if ! ss_output=$(ss -tulpn | grep -E ":${port}\b"); then
        log "✅ 端口 $port 空闲"
        return 0
    fi

    # ✅ 3. 提取 PID（如果有）
    local pid
    pid=$(echo "$ss_output" | sed -r 's/.*pid=([0-9]+),.*/\1/' | head -1)

    local proc_name="unknown"
    if [ -n "$pid" ] && [ -d "/proc/$pid" ]; then
        # 尝试获取进程名
        proc_name=$(ps -p "$pid" -o comm= 2>/dev/null || echo "unknown")
    fi

    # ✅ 4. 清理并转小写
    proc_name=$(echo "$proc_name" | tr '[:upper:]' '[:lower:]' | xargs)

    # ✅ 5. 如果指定了预期进程
    if [ -n "$expected_process" ]; then
        local expected=$(echo "$expected_process" | tr '[:upper:]' '[:lower:]')

        if [[ "$proc_name" == *"$expected"* ]]; then
            log "✅ 端口 $port 已被预期服务 ($proc_name) 占用，继续..."
            return 0
        else
            local cmd=$(echo "$ss_output" | awk '{ $1=$2=$3=$4=""; gsub(/^ +/, ""); print }')
            error_exit "❌ 端口 $port 被非预期进程占用: $proc_name (命令: $cmd)"
        fi
    else
        local cmd=$(echo "$ss_output" | awk '{ $1=$2=$3=$4=""; gsub(/^ +/, ""); print }')
        error_exit "❌ 端口 $port 已被占用 (PID: $pid, 进程: $proc_name, 命令: $cmd)"
    fi
}


# ==================== 写入系统环境变量（持久化）====================
# 确保变量能在 Java 中通过 System.getenv() 读取
persist_env_var() {
    local key="$1"
    local value="$2"
    local env_file="/etc/environment"

    # 创建临时文件
    local temp_file=$(mktemp)

    # 标记是否已存在
    local found=false

    # 读取原文件，替换或保留
    while IFS= read -r line; do
        if [[ "$line" =~ ^export[[:space:]]+$key= ]] || [[ "$line" =~ ^$key= ]]; then
            echo "export $key='$value'" >> "$temp_file"
            found=true
        else
            echo "$line" >> "$temp_file"
        fi
    done < "$env_file"

    # 如果未找到，追加
    if [ "$found" = false ]; then
        echo "export $key='$value'" >> "$temp_file"
    fi

    # 备份原文件
    sudo cp "$env_file" "$env_file.bak.$(date +%Y%m%d-%H%M%S)"

    # 写回（需要 root 权限）
    sudo cp "$temp_file" "$env_file"
    sudo chmod 644 "$env_file"

    # 清理
    rm -f "$temp_file"

#    log "已持久化环境变量: $key=$value"
}