#!/bin/bash

# ===================================================================
# 01__env_check.sh - 环境检查与资源配置收集
# 功能：检查系统环境、收集资源配置、保存到环境变量
# ===================================================================

source "$(dirname "$0")/00__config.sh"

log "开始系统环境检查..."




create_env_file() {
    sudo mkdir -p "$NSE_PROJECT_DIR/config"
    local file="$NSE_PROJECT_DIR/config/nse-global.conf"
    cat >> "$file" << EOF
NSE_INIT_CPU_CORES=$CPU_CORES_PER_NODE
NSE_INIT_MAX_MEMORY=$MEMORY_PER_NODE_GB
NSE_INIT_MAX_DISK=$DISK_PER_NODE_GB
LIC_CONCURRENCY=$LIC_CONCURRENCY
EOF
    chmod 600 "$file"
}

upsert_env_file() {
  log "调用了upsert_env_file"
    local config_file="$NSE_PROJECT_DIR/config/nse-global.conf"
    local temp_file="$(mktemp)"

    # 确保目录存在
    sudo mkdir -p "$(dirname "$config_file")"
    sudo chown "$USER":"$USER" "$(dirname "$config_file")"

    # 定义要写入的变量（可以做成参数）
    local vars=(
        "NSE_INIT_CPU_CORES=$CPU_CORES_PER_NODE"
        "NSE_INIT_MAX_MEMORY=$MEMORY_PER_NODE_GB"
        "NSE_INIT_MAX_DISK=$DISK_PER_NODE_GB"
        "LIC_CONCURRENCY=$LIC_CONCURRENCY"
    )

    # 使用 awk 处理：匹配 KEY= 的行，先删除或更新，最后统一追加缺失的
    {
        # 1. 先读原文件，跳过匹配的变量
        awk -v vars="${vars[*]}" '
        BEGIN {
            split(vars, arr, " ")
            for (i in arr) {
                match(arr[i], /([^=]+)=(.*)/, cap)
                keys[cap[1]] = arr[i]   # keys["DB_HOST"] = "DB_HOST=localhost"
            }
        }
        /^[[:space:]]*[^[:space:]#]/ {
            match($0, /([^= \t#]+)[ \t=]/, name)
            if (name[1] in keys) {
                # 跳过这一行，后面统一写
                next
            }
        }
        { print }
        ' "$config_file" 2>/dev/null || true

        # 2. 输出所有变量（覆盖或追加）
        for v in "${vars[@]}"; do
            echo "$v"
        done
    } > "$temp_file"

    # 3. 移动临时文件
    mv "$temp_file" "$config_file"

    # 4. 设置权限
    chmod 600 "$config_file"
    sudo chown "$USER":"$USER" "$config_file"

    log "配置文件已更新（覆盖或追加）: $config_file"
}

# ==================== 基础环境检查 ====================

# 检查操作系统
if ! grep -q "Ubuntu" /etc/os-release; then
    error_exit "此脚本仅支持Ubuntu系统"
fi

log "检测到Ubuntu版本: $(lsb_release -rs)"

# ==================== 收集用户输入 ====================

log "请输入资源配置参数："

# 输入LIC并发数
while true; do
    read -p "请输入LIC并发数 (1-1000): " LIC_CONCURRENCY
    if [[ "$LIC_CONCURRENCY" =~ ^[0-9]+$ ]] && [ "$LIC_CONCURRENCY" -ge 1 ] && [ "$LIC_CONCURRENCY" -le 1000 ]; then
        break
    else
        log "错误：请输入1-1000之间的整数"
    fi
done

# 输入每个节点CPU核心数
while true; do
    read -p "请输入每个节点分配的CPU核心数 (1-64): " CPU_CORES_PER_NODE
    if [[ "$CPU_CORES_PER_NODE" =~ ^[0-9]+$ ]] && [ "$CPU_CORES_PER_NODE" -ge 1 ] && [ "$CPU_CORES_PER_NODE" -le 64 ]; then
        break
    else
        log "错误：请输入1-64之间的整数"
    fi
done

# 输入内存大小(GB)
while true; do
    read -p "请输入每个节点分配的内存大小(GB) (1-512): " MEMORY_PER_NODE_GB
    if [[ "$MEMORY_PER_NODE_GB" =~ ^[0-9]+$ ]] && [ "$MEMORY_PER_NODE_GB" -ge 1 ] && [ "$MEMORY_PER_NODE_GB" -le 512 ]; then
        break
    else
        log "错误：请输入1-512之间的整数"
    fi
done

# 输入磁盘空间大小(GB)
while true; do
    read -p "请输入每个节点分配的磁盘空间大小(GB) (1-10240): " DISK_PER_NODE_GB
    if [[ "$DISK_PER_NODE_GB" =~ ^[0-9]+$ ]] && [ "$DISK_PER_NODE_GB" -ge 1 ] && [ "$DISK_PER_NODE_GB" -le 10240 ]; then
        break
    else
        log "错误：请输入1-10240之间的整数"
    fi
done

# ==================== 计算总资源需求 ====================

TOTAL_CPU_REQUIRED=$((LIC_CONCURRENCY * CPU_CORES_PER_NODE))
TOTAL_MEMORY_REQUIRED_GB=$((LIC_CONCURRENCY * MEMORY_PER_NODE_GB))
TOTAL_DISK_REQUIRED_GB=$((LIC_CONCURRENCY * DISK_PER_NODE_GB))

# ==================== 资源检查函数 ====================

check_cpu_resources() {
    log "检查CPU资源..."
    local available=$(nproc)
    log "系统可用CPU核心数: $available"

    if [ "$available" -lt "$TOTAL_CPU_REQUIRED" ]; then
        log "⚠️  警告：CPU核心数不足！需要 $TOTAL_CPU_REQUIRED 核心，但系统只有 $available 核心"
        CPU_CHECK_PASSED=false
    else
        log "✓ CPU核心数检查通过 ($available >= $TOTAL_CPU_REQUIRED)"
        CPU_CHECK_PASSED=true
    fi
}

check_memory_resources() {
    log "检查内存资源..."
    local available_mb=$(free -m | awk 'NR==2{print $2}')
    local available_gb=$((available_mb / 1024))
    local required_mb=$((TOTAL_MEMORY_REQUIRED_GB * 1024))

    log "系统可用内存: ${available_gb}GB (${available_mb}MB)"

    if [ "$available_mb" -lt "$required_mb" ]; then
        log "⚠️  警告：内存不足！需要 ${TOTAL_MEMORY_REQUIRED_GB}GB，但系统只有 ${available_gb}GB"
        MEMORY_CHECK_PASSED=false
    else
        log "✓ 内存检查通过 (${available_gb}GB >= ${TOTAL_MEMORY_REQUIRED_GB}GB)"
        MEMORY_CHECK_PASSED=true
    fi
}

check_disk_resources() {
    log "检查磁盘空间..."
    local available_kb=$(df / | awk 'NR==2{print $4}')
    local available_gb=$((available_kb / 1024 / 1024))
    local required_kb=$((TOTAL_DISK_REQUIRED_GB * 1024 * 1024))

    log "系统可用磁盘空间: ${available_gb}GB"

    if [ "$available_kb" -lt "$required_kb" ]; then
        log "⚠️  警告：磁盘空间不足！需要 ${TOTAL_DISK_REQUIRED_GB}GB，但系统只有 ${available_gb}GB"
        DISK_CHECK_PASSED=false
    else
        log "✓ 磁盘空间检查通过 (${available_gb}GB >= ${TOTAL_DISK_REQUIRED_GB}GB)"
        DISK_CHECK_PASSED=true
    fi
}

# ==================== 执行检查 ====================

CPU_CHECK_PASSED=true
MEMORY_CHECK_PASSED=true
DISK_CHECK_PASSED=true

check_cpu_resources
check_memory_resources
check_disk_resources

# ==================== 检查端口 ====================
check_port $DB_PORT "postgres"
check_port $NSE_MGR_PORT

# ==================== 检查离线包 ====================

if [ ! -d "$SOFTS_DIR" ]; then
    error_exit "离线包目录不存在: $SOFTS_DIR"
fi

# ==================== 资源不足处理 ====================

if [ "$CPU_CHECK_PASSED" = false ] || [ "$MEMORY_CHECK_PASSED" = false ] || [ "$DISK_CHECK_PASSED" = false ]; then
    log ""
    log "⚠️  检测到资源不足的情况！"
    log "继续安装可能会导致系统性能问题或安装失败。"
    log ""

    while true; do
        read -p "是否要继续安装？(y/n): " continue_install
        case $continue_install in
            [Yy]* )
                log "用户选择继续安装，正在保存资源配置..."
                break
                ;;
            [Nn]* )
                log "用户选择取消安装"
                error_exit "安装已取消"
                ;;
            * )
                log "请输入 y 或 n"
                ;;
        esac
    done
fi

# ==================== 保存配置到临时文件 ====================
CONFIG_FILE=$NSE_PROJECT_DIR"/nse-resource-config/nse-resource-config.env"
sudo mkdir -p $NSE_PROJECT_DIR"/nse-resource-config"
cat > "$CONFIG_FILE" << EOF
# NSE资源配置 - 由01__env_check.sh生成
export LIC_CONCURRENCY="$LIC_CONCURRENCY"
export NSE_INIT_CPU_CORES="$CPU_CORES_PER_NODE"
export NSE_INIT_MAX_MEMORY="$MEMORY_PER_NODE_GB"
export NSE_INIT_MAX_DISK="$DISK_PER_NODE_GB"
export TOTAL_CPU_REQUIRED="$TOTAL_CPU_REQUIRED"
export TOTAL_MEMORY_REQUIRED_GB="$TOTAL_MEMORY_REQUIRED_GB"
export TOTAL_DISK_REQUIRED_GB="$TOTAL_DISK_REQUIRED_GB"
EOF

log "资源配置已保存到: $CONFIG_FILE"
log "================================================="

log "系统环境检查通过"
save_state "env_checked"

upsert_env_file