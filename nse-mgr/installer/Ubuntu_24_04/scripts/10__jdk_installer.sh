#!/bin/bash

# ===================================================================
# 10-base-deps.sh - 基础依赖安装（JDK、Node.js、Python、Nginx）
# ===================================================================

source "$(dirname "$0")/00__config.sh"

# ==================== 安装 JDK 21 ====================

install_jdk() {
    log "开始检查 JDK 21 安装状态..."

    # 检查是否已安装 JDK 21
    if command -v java &>/dev/null; then
        local java_version
        java_version=$(java -version 2>&1 | head -n1)
        if [[ "$java_version" == *"21"* ]]; then
            log "✅ JDK 21 已安装：$java_version"
            return 0
        else
            log "⚠️  检测到旧版本 JDK：$java_version，将覆盖安装 JDK 21"
        fi
    fi

    # 查找 JDK 安装包
    log "正在查找 JDK 21 安装包..."
    local jdk_tar=$(find "$SOFTS_DIR" -name "jdk-21*_linux-x64_bin.tar.gz" | head -1)

    if [ ! -f "$jdk_tar" ]; then
        error_exit "未找到 JDK 21 的 .tar.gz 包，请检查 softs/ 目录"
    fi

    log "✅ 找到 JDK 安装包: $(basename "$jdk_tar")"
    log "包大小: $(du -h "$jdk_tar" | cut -f1)"

    # 解压 JDK
    log "正在解压 JDK 到 /opt/ ..."
    if sudo tar -zxf "$jdk_tar" -C /opt/; then
        log "✅ JDK 解压成功"
    else
        error_exit "JDK 解压失败，请检查压缩包完整性"
    fi

    # 创建软链接
    local jdk_install_path="/opt/jdk-21.0.8"  # 自动识别目录名

    # 设置环境变量
    export JAVA_HOME="$jdk_install_path"
    export PATH="$JAVA_HOME/bin:$PATH"

    log "配置系统环境变量..."
    # 删除旧的 JAVA_HOME 行（避免重复）
    sudo sed -i '/JAVA_HOME/d' /etc/profile
    sudo tee -a /etc/profile > /dev/null << 'EOF'
export JAVA_HOME=/opt/jdk-21.0.8
export JRE_HOME=${JAVA_HOME}/jre
export CLASSPATH=.:${JAVA_HOME}/lib:${JRE_HOME}/lib
export PATH=${JAVA_HOME}/bin:$PATH
EOF

    # 重新加载环境变量
    source /etc/profile

    log "Java 环境变量配置完成。"

    # 验证安装
    log "验证 JDK 安装..."
    if java -version 2>&1 | grep -q "21"; then
        log "✅ JDK 21 安装成功：$(java -version 2>&1 | head -n1)"
    else
        error_exit "JDK 安装后验证失败"
    fi

    save_state "jdk_installed"
}

# ==================== 主流程 ====================

log "开始安装基础依赖..."

if install_jdk; then
    log "JDK 安装阶段完成"
else
    error_exit "JDK 安装失败"
fi