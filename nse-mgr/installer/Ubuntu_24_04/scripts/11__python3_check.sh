#!/bin/bash
source "$(dirname "$0")/00__config.sh"

install_python3() {
    if command -v python3 &>/dev/null; then
        log "Python3 已安装"
        return 0
    fi

    local py_tar="$SOFTS_DIR/Python-3.11.9.tgz"
    if [ ! -f "$py_tar" ]; then
        error_exit "未找到 Python 包: $py_tar"
    fi

    local build=$(mktemp -d)
    tar -xzf "$py_tar" -C "$build" --strip-components=1
    cd "$build"

    ./configure --prefix=/usr/local --enable-optimizations --with-ensurepip=install
    make -j$(nproc) && sudo make altinstall

    cd /
    rm -rf "$build"

    # 创建软链接
    sudo ln -sf /usr/local/bin/python3.11 /usr/bin/python3

    # 配置动态库路径
    echo '/usr/local/lib' | sudo tee /etc/ld.so.conf.d/python3.11.conf
    sudo ldconfig

    log "Python3 安装完成"
    save_state "python3_installed"
}

# 执行安装
log "开始检查Python环境..."
install_python3