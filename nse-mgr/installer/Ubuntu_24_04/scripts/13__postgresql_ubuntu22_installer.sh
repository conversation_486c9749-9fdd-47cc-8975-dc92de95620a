#!/bin/bash

source "$(dirname "$0")/00__config.sh"

# Ubuntu 22.04 PostgreSQL 配置路径
PG_VERSION="14"  # Ubuntu 22.04 默认 PostgreSQL 版本
PG_CONF_PATH="/etc/postgresql/$PG_VERSION/main/postgresql.conf"
PG_HBA_PATH="/etc/postgresql/$PG_VERSION/main/pg_hba.conf"
PG_SERVICE_USER="postgres"
PG_SERVICE_NAME="postgresql"

# ==================== 检查系统版本 ====================
check_ubuntu_version() {
    log "检查 Ubuntu 版本..."
    
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        if [[ "$VERSION_ID" == "22.04" ]]; then
            log "✅ 检测到 Ubuntu 22.04 LTS"
        else
            log "⚠️  警告：当前系统版本为 $VERSION_ID，脚本针对 Ubuntu 22.04 优化"
            read -p "是否继续安装？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                error_exit "用户取消安装"
            fi
        fi
    else
        log "⚠️  无法检测系统版本，假设为 Ubuntu 22.04"
    fi
}

# ==================== 安装 PostgreSQL ====================
install_postgresql() {
    log "开始安装 PostgreSQL..."
    
    # 更新包列表
    log "1. 更新系统包列表..."
    apt update || error_exit "更新包列表失败"
    
    # 安装 PostgreSQL
    log "2. 安装 PostgreSQL 及相关组件..."
    apt install -y postgresql postgresql-contrib postgresql-client || error_exit "PostgreSQL 安装失败"
    
    # 检查安装结果
    if command -v psql >/dev/null 2>&1; then
        PG_VERSION=$(psql --version | grep -oE '[0-9]+\.[0-9]+' | head -1 | cut -d. -f1)
        log "✅ PostgreSQL $PG_VERSION 安装成功"
        
        # 更新配置路径
        PG_CONF_PATH="/etc/postgresql/$PG_VERSION/main/postgresql.conf"
        PG_HBA_PATH="/etc/postgresql/$PG_VERSION/main/pg_hba.conf"
    else
        error_exit "PostgreSQL 安装验证失败"
    fi
}

# ==================== 配置 PostgreSQL ====================
configure_postgresql() {
    log "开始配置 PostgreSQL 数据库..."

    # 1. 配置 postgresql.conf 允许远程访问
    log "1. 配置 postgresql.conf..."
    if [ -f "$PG_CONF_PATH" ]; then
        # 备份原始文件
        cp "$PG_CONF_PATH" "$PG_CONF_PATH.bak.$(date +%Y%m%d-%H%M%S)"
        
        # 修改 listen_addresses
        if grep -q "^#listen_addresses = 'localhost'" "$PG_CONF_PATH"; then
            sed -i "s/^#listen_addresses = 'localhost'/listen_addresses = '*'/" "$PG_CONF_PATH"
            log "   ✅ listen_addresses 已修改为 '*'"
        elif grep -q "^listen_addresses = 'localhost'" "$PG_CONF_PATH"; then
            sed -i "s/^listen_addresses = 'localhost'/listen_addresses = '*'/" "$PG_CONF_PATH"
            log "   ✅ listen_addresses 已修改为 '*'"
        else
            echo "listen_addresses = '*'" >> "$PG_CONF_PATH"
            log "   ✅ 已添加 listen_addresses = '*'"
        fi
        
        # 修改端口配置（如果需要）
        if ! grep -q "^port = $DB_PORT" "$PG_CONF_PATH"; then
            sed -i "s/^#port = 5432/port = $DB_PORT/" "$PG_CONF_PATH"
            sed -i "s/^port = 5432/port = $DB_PORT/" "$PG_CONF_PATH"
            log "   ✅ 端口已设置为 $DB_PORT"
        fi
    else
        error_exit "未找到 postgresql.conf 文件: $PG_CONF_PATH"
    fi

    # 2. 配置 pg_hba.conf 允许远程认证
    log "2. 配置 pg_hba.conf..."
    if [ -f "$PG_HBA_PATH" ]; then
        # 备份原始文件
        cp "$PG_HBA_PATH" "$PG_HBA_PATH.bak.$(date +%Y%m%d-%H%M%S)"
        
        # 检查是否已存在远程认证规则，避免重复添加
        if ! grep -q "host.*all.*all.*0.0.0.0/0.*md5" "$PG_HBA_PATH"; then
            echo "# NSE Remote Access Rule" >> "$PG_HBA_PATH"
            echo "host    all             all             0.0.0.0/0               md5" >> "$PG_HBA_PATH"
            log "   ✅ 已添加远程认证规则"
        else
            log "   ℹ️  远程认证规则已存在"
        fi
    else
        error_exit "未找到 pg_hba.conf 文件: $PG_HBA_PATH"
    fi

    # 3. 重启 PostgreSQL 服务以应用配置更改
    log "3. 重启 PostgreSQL 服务..."
    systemctl restart $PG_SERVICE_NAME || error_exit "重启 PostgreSQL 服务失败"
    
    # 等待服务启动
    sleep 3
    if systemctl is-active --quiet $PG_SERVICE_NAME; then
        log "   ✅ PostgreSQL 服务已重启并运行中"
    else
        error_exit "PostgreSQL 服务启动失败"
    fi
}

# ==================== 创建数据库和用户 ====================
create_database_and_user() {
    log "4. 创建数据库和用户..."
    
    # 检查用户是否已存在
    if sudo -u "$PG_SERVICE_USER" psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='$DEFAULT_DB_USER'" | grep -q 1; then
        log "   ℹ️  用户 '$DEFAULT_DB_USER' 已存在，跳过创建"
    else
        sudo -u "$PG_SERVICE_USER" psql -c "CREATE USER \"$DEFAULT_DB_USER\" WITH ENCRYPTED PASSWORD '$DEFAULT_DB_PASS';" || error_exit "创建用户失败"
        log "   ✅ 用户 '$DEFAULT_DB_USER' 创建成功"
    fi
    
    # 检查数据库是否已存在
    if sudo -u "$PG_SERVICE_USER" psql -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
        log "   ℹ️  数据库 '$DB_NAME' 已存在，跳过创建"
    else
        sudo -u "$PG_SERVICE_USER" psql -c "CREATE DATABASE \"$DB_NAME\" OWNER \"$DEFAULT_DB_USER\";" || error_exit "创建数据库失败"
        log "   ✅ 数据库 '$DB_NAME' 创建成功"
    fi

    # 5. 授予权限
    log "5. 授予权限..."
    sudo -u "$PG_SERVICE_USER" psql -d "$DB_NAME" -c "GRANT ALL PRIVILEGES ON DATABASE \"$DB_NAME\" TO \"$DEFAULT_DB_USER\";" || error_exit "授予数据库权限失败"
    sudo -u "$PG_SERVICE_USER" psql -d "$DB_NAME" -c "GRANT ALL ON SCHEMA \"$SCHEMA_NAME\" TO \"$DEFAULT_DB_USER\";" || error_exit "授予模式权限失败"
    sudo -u "$PG_SERVICE_USER" psql -d "$DB_NAME" -c "ALTER USER \"$DEFAULT_DB_USER\" CREATEDB;" || error_exit "授予创建数据库权限失败"
    log "   ✅ 权限授予完成"
}

# ==================== 初始化数据库结构 ====================
initialize_database_schema() {
    log "6. 初始化数据库结构..."
    
    local sql_file="$ROOT_DIR/../../../db/V1.0.0_init_db.sql"
    if [ -f "$sql_file" ]; then
        log "   正在执行数据库初始化脚本..."
        sudo -u "$PG_SERVICE_USER" psql -d "$DB_NAME" -f "$sql_file" || {
            log "   ⚠️  数据库初始化脚本执行失败，但继续安装"
        }
        log "   ✅ 数据库结构初始化完成"
    else
        log "   ⚠️  未找到数据库初始化脚本: $sql_file"
    fi
}

# ==================== 注册为系统服务 ====================
register_as_service() {
    log "7. 配置 PostgreSQL 开机自启..."
    
    if command -v systemctl &>/dev/null; then
        systemctl enable $PG_SERVICE_NAME || log "   ⚠️  启用服务失败，可能已经启用"
        log "   ✅ PostgreSQL 已配置为开机自启"
    else
        log "   ⚠️  未找到 systemd，无法配置开机自启"
    fi
}

# ==================== 验证安装 ====================
verify_installation() {
    log "8. 验证 PostgreSQL 安装..."
    
    # 检查服务状态
    if systemctl is-active --quiet $PG_SERVICE_NAME; then
        log "   ✅ PostgreSQL 服务运行正常"
    else
        error_exit "PostgreSQL 服务未运行"
    fi
    
    # 检查端口监听
    if netstat -tlnp 2>/dev/null | grep -q ":$DB_PORT.*LISTEN" || ss -tlnp 2>/dev/null | grep -q ":$DB_PORT.*LISTEN"; then
        log "   ✅ PostgreSQL 正在监听端口 $DB_PORT"
    else
        log "   ⚠️  PostgreSQL 可能未正确监听端口 $DB_PORT"
    fi
    
    # 测试数据库连接
    if sudo -u "$PG_SERVICE_USER" psql -d "$DB_NAME" -c "SELECT version();" >/dev/null 2>&1; then
        log "   ✅ 数据库连接测试成功"
    else
        log "   ⚠️  数据库连接测试失败"
    fi
    
    log "✅ PostgreSQL 安装和配置完成！"
    log ""
    log "📋 数据库信息："
    log "   主机: localhost"
    log "   端口: $DB_PORT"
    log "   数据库: $DB_NAME"
    log "   用户: $DEFAULT_DB_USER"
    log "   密码: $DEFAULT_DB_PASS"
    log ""
    log "🔧 管理命令："
    log "   查看状态: systemctl status $PG_SERVICE_NAME"
    log "   重启服务: systemctl restart $PG_SERVICE_NAME"
    log "   连接数据库: psql -h localhost -p $DB_PORT -U $DEFAULT_DB_USER -d $DB_NAME"
}

# ==================== 主执行流程 ====================
main() {
    log "🚀 开始 PostgreSQL 安装和配置 (Ubuntu 22.04)"
    
    # 检查是否已安装
    if is_state_saved "postgresql_configured"; then
        log "ℹ️  PostgreSQL 已配置，跳过安装"
        return 0
    fi
    
    check_ubuntu_version
    install_postgresql
    configure_postgresql
    create_database_and_user
    initialize_database_schema
    register_as_service
    verify_installation
    
    save_state "postgresql_configured"
    log "🎉 PostgreSQL 安装配置完成！"
}

# 执行主函数
main "$@"
