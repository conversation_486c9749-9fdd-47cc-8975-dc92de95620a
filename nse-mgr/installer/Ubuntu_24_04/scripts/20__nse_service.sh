#!/bin/bash
source "$(dirname "$0")/00__config.sh"

deploy_backend() {
    [ ! -f "$PROJECTS_DIR/$NSE_PROJECT_NAME" ] && error_exit "未找到服务包：$PROJECTS_DIR/$NSE_PROJECT_NAME"

    sudo mkdir -p "$NSE_PROJECT_DIR/projects"
    cp -r "$PROJECTS_DIR" "$NSE_PROJECT_DIR"
    cd "$NSE_PROJECT_DIR"

#    create_env_file
    create_service
    log "后端服务部署完成"

    # 启动服务
    sudo systemctl start nse-service

    # 检查是否启动成功
    if ! sudo systemctl is-active --quiet nse-service; then
        error_exit "服务启动失败，请检查日志：journalctl -u nse-service -n 50"
    fi

    save_state "backend_deployed"

     echo -e "\n\033[1;36m🚀 NSE 服务 (nse-service) 已启动，正在输出启动日志...\033[0m"
    echo -e "--------------------------------------------------------"

    # ✅ 打印最近 20 行日志（不实时）
    echo -e "\033[1;34m📝 最近的启动日志（最新 20 行）：\033[0m"
    sudo journalctl -u nse-service -n 20 --no-pager

    # ✅ 可选：如果你想“跟随”日志（像 tail -f），取消下一行注释
    # echo -e "\n\033[1;32m⏳ 正在实时跟踪日志... (按 Ctrl+C 退出)\033[0m"
    # sudo journalctl -u nse-service -f --no-pager

    echo -e "--------------------------------------------------------"
    echo -e "\033[1;32m💡 建议命令：\033[0m"
    echo -e "  查看完整日志: \033[1;33mjournalctl -u nse-service\033[0m"
    echo -e "  实时跟踪日志: \033[1;33mjournalctl -u nse-service -f\033[0m"
    echo -e "  重启服务: \033[1;33msudo systemctl restart nse-service\033[0m"

    sudo systemctl status nse-service
}

#create_env_file() {
#    sudo mkdir -p "$NSE_PROJECT_DIR/config"
#    local file="$NSE_PROJECT_DIR/config/nse-global.conf"
#    cat > "$file" << EOF
#DB_HOST=localhost
#DB_PORT=$DB_PORT
#DB_NAME=$DB_NAME
#DB_USER=$DB_USER
#DB_PASSWORD=$DB_PASS
#NSE_INIT_CPU_CORES=$CPU_CORES_PER_NODE
#NSE_INIT_MAX_MEMORY=$MEMORY_PER_NODE_GB
#NSE_INIT_MAX_DISK=$DISK_PER_NODE_GB
#LIC_CONCURRENCY=$LIC_CONCURRENCY
#EOF
#    chmod 600 "$file"
#}

create_service() {
    sudo tee /etc/systemd/system/nse-service.service > /dev/null << EOF
[Unit]
Description=NSE Service
After=postgresql.service
[Service]
User=root
WorkingDirectory=$NSE_PROJECT_DIR
EnvironmentFile=$NSE_PROJECT_DIR/config/nse-global.conf
ExecStart=/opt/jdk-21.0.8/bin/java -jar $NSE_PROJECT_DIR/projects/$NSE_PROJECT_NAME
Restart=always
[Install]
WantedBy=multi-user.target
EOF
    sudo systemctl daemon-reload
    sudo systemctl enable nse-service
}

deploy_backend
sudo tail -100f $NSE_PROJECT_DIR/logs/mgr-info.log