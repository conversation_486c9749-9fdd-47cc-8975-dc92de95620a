#!/bin/bash

# PostgreSQL 17 完整卸载脚本
# 适用于 Ubuntu 24.04 离线安装的 PostgreSQL 17

# 导入配置文件
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../00__config.sh"

# ==================== 卸载函数 ====================

# 停止所有 PostgreSQL 服务
stop_postgresql_services() {
    log "🛑 停止 PostgreSQL 服务..."

    local services=(
        "postgresql@17-main"
        "postgresql"
        "postgresql.service"
    )

    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service" 2>/dev/null; then
            log "停止服务: $service"
            sudo systemctl stop "$service" || true
        fi

        if systemctl is-enabled --quiet "$service" 2>/dev/null; then
            log "禁用服务: $service"
            sudo systemctl disable "$service" || true
        fi
    done

    log "✅ PostgreSQL 服务已停止"
}

# 卸载 PostgreSQL 包
remove_postgresql_packages() {
    log "📦 卸载 PostgreSQL 包..."

    # PostgreSQL 17 相关包列表
    local pg_packages=(
        "postgresql-17"
        "postgresql-client-17"
        "postgresql-client"
        "postgresql-common"
        "postgresql-common-dev"
        "libpq5"
        "libpq-dev"
        "postgresql-client-common"
    )

    # 使用 apt purge 卸载
    for package in "${pg_packages[@]}"; do
        if dpkg-query -W -f='${Status}' "$package" 2>/dev/null | grep -q "install ok installed"; then
            log "卸载包: $package"
            sudo apt purge -y "$package" 2>/dev/null || true
        fi
    done

    # 使用 dpkg 强制清除残留配置
    log "清除残留配置..."
    for package in "${pg_packages[@]}"; do
        sudo dpkg --purge "$package" 2>/dev/null || true
    done

    log "✅ PostgreSQL 包卸载完成"
}

# 删除用户和组
remove_postgres_user() {
    log "👤 删除 postgres 用户和组..."

    # 检查并删除 postgres 用户
    if id "postgres" &>/dev/null; then
        log "删除 postgres 用户"
        sudo userdel -r postgres 2>/dev/null || true
    fi

    # 检查并删除 postgres 组
    if getent group postgres &>/dev/null; then
        log "删除 postgres 组"
        sudo groupdel postgres 2>/dev/null || true
    fi

    log "✅ postgres 用户和组已删除"
}

# 删除数据目录和配置文件
remove_postgresql_data() {
    log "🗂️  删除 PostgreSQL 数据和配置文件..."

    local directories=(
        "/var/lib/postgresql"
        "/etc/postgresql"
        "/var/log/postgresql"
        "/usr/lib/postgresql/17"
        "/usr/share/postgresql/17"
        "/var/run/postgresql"
        "/tmp/.s.PGSQL.*"
    )

    for dir in "${directories[@]}"; do
        if [ -e "$dir" ]; then
            log "删除目录: $dir"
            sudo rm -rf "$dir"
        fi
    done

    # 删除可能的符号链接和二进制文件
    local binaries=(
        "/usr/bin/psql"
        "/usr/bin/pg_dump"
        "/usr/bin/pg_restore"
        "/usr/bin/createdb"
        "/usr/bin/dropdb"
        "/usr/bin/postgres"
        "/usr/bin/postmaster"
    )

    for binary in "${binaries[@]}"; do
        if [ -L "$binary" ] || [ -f "$binary" ]; then
            log "删除二进制文件: $binary"
            sudo rm -f "$binary"
        fi
    done

    log "✅ PostgreSQL 数据和配置文件已删除"
}

# 清理系统配置
cleanup_system_config() {
    log "🧹 清理系统配置..."

    # 清理环境变量
    local env_files=(
        "/etc/environment"
        "/etc/profile"
        "/etc/bash.bashrc"
    )

    for env_file in "${env_files[@]}"; do
        if [ -f "$env_file" ]; then
            # 删除 PostgreSQL 相关的环境变量
            sudo sed -i '/PGDATA\|PGUSER\|PGDATABASE\|PGPORT\|PGHOST/d' "$env_file" 2>/dev/null || true
        fi
    done

    # 清理 systemd 配置
    sudo systemctl daemon-reload

    # 清理包管理器缓存
    sudo apt autoremove -y
    sudo apt autoclean

    log "✅ 系统配置清理完成"
}

# 清理部署状态
cleanup_deployment_state() {
    log "📋 清理部署状态..."

    if [ -f "$DEPLOYMENT_STATE_FILE" ]; then
        # 删除数据库相关的状态标记
        sudo sed -i '/database_configured\|postgresql_installed/d' "$DEPLOYMENT_STATE_FILE" 2>/dev/null || true
        log "✅ 部署状态已清理"
    fi
}

# 验证卸载结果
verify_uninstall() {
    log "🔍 验证卸载结果..."

    local issues=()

    # 检查包是否还存在
    if dpkg-query -W -f='${Status}' "postgresql-17" 2>/dev/null | grep -q "install ok installed"; then
        issues+=("PostgreSQL 17 包仍然存在")
    fi

    # 检查服务是否还在运行
    if systemctl is-active --quiet "postgresql@17-main" 2>/dev/null; then
        issues+=("PostgreSQL 服务仍在运行")
    fi

    # 检查用户是否还存在
    if id "postgres" &>/dev/null; then
        issues+=("postgres 用户仍然存在")
    fi

    # 检查关键目录是否还存在
    if [ -d "/var/lib/postgresql/17" ]; then
        issues+=("数据目录仍然存在")
    fi

    # 检查二进制文件
    if command -v psql &>/dev/null; then
        issues+=("psql 命令仍然可用")
    fi

    if [ ${#issues[@]} -eq 0 ]; then
        log "✅ PostgreSQL 17 已完全卸载"
        return 0
    else
        log "⚠️  卸载可能不完整，发现以下问题："
        for issue in "${issues[@]}"; do
            log "  - $issue"
        done
        return 1
    fi
}

# ==================== 主流程 ====================

main() {
    log "🚀 开始卸载 PostgreSQL 17..."

    # 检查是否跳过确认（用于自动化脚本）
    if [ "$SKIP_CONFIRMATION" != "true" ]; then
        # 确认操作
        echo "⚠️  警告：此操作将完全删除 PostgreSQL 17 及其所有数据！"
        echo "包括："
        echo "  - 所有数据库和数据"
        echo "  - 配置文件"
        echo "  - postgres 用户"
        echo "  - 相关软件包"
        echo ""
        read -p "确定要继续吗？(输入 'YES' 确认): " confirm

        if [ "$confirm" != "YES" ]; then
            log "❌ 用户取消操作"
            exit 0
        fi
    else
        log "🤖 自动化模式：跳过用户确认"
    fi

    # 执行卸载步骤
    stop_postgresql_services
    remove_postgresql_packages
    remove_postgres_user
    remove_postgresql_data
    cleanup_system_config
    cleanup_deployment_state

    # 验证结果
    if verify_uninstall; then
        log "🎉 PostgreSQL 17 卸载完成！"
        exit 0
    else
        log "⚠️  卸载可能不完整，请检查上述问题"
        exit 1
    fi
}

# 执行主流程
main "$@"