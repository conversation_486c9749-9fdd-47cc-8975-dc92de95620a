#!/bin/bash

# PostgreSQL 17 脚本测试工具
# 用于验证安装和卸载脚本的语法和逻辑

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

# 测试脚本语法
test_script_syntax() {
    local script_path="$1"
    local script_name="$(basename "$script_path")"
    
    log "🔍 测试脚本语法: $script_name"
    
    if [ ! -f "$script_path" ]; then
        error "脚本文件不存在: $script_path"
        return 1
    fi
    
    if bash -n "$script_path"; then
        log "✅ 语法检查通过: $script_name"
        return 0
    else
        error "语法检查失败: $script_name"
        return 1
    fi
}

# 测试脚本依赖
test_script_dependencies() {
    local script_path="$1"
    local script_name="$(basename "$script_path")"
    
    log "🔍 测试脚本依赖: $script_name"
    
    # 检查 source 的文件是否存在
    local source_files=$(grep -o 'source.*\.sh' "$script_path" 2>/dev/null || true)
    
    if [ -n "$source_files" ]; then
        while IFS= read -r source_line; do
            # 提取文件路径
            local source_file=$(echo "$source_line" | sed 's/source[[:space:]]*["'\'']*\([^"'\'']*\)["'\'']*$/\1/')
            
            # 处理相对路径
            if [[ "$source_file" == *'$(dirname "$0")'* ]]; then
                source_file=$(echo "$source_file" | sed "s|\$(dirname \"\$0\")|$(dirname "$script_path")|")
            fi
            
            if [ ! -f "$source_file" ]; then
                error "依赖文件不存在: $source_file"
                return 1
            else
                log "✅ 依赖文件存在: $(basename "$source_file")"
            fi
        done <<< "$source_files"
    fi
    
    log "✅ 依赖检查通过: $script_name"
    return 0
}

# 测试配置文件
test_config_file() {
    local config_file="$SCRIPT_DIR/scripts/00__config.sh"
    
    log "🔍 测试配置文件"
    
    if [ ! -f "$config_file" ]; then
        error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 检查关键变量是否定义
    local required_vars=(
        "DEFAULT_DB_USER"
        "DEFAULT_DB_PASS"
        "DB_NAME"
        "SCHEMA_NAME"
        "DB_PORT"
        "SOFTS_DIR"
        "DEPLOYMENT_STATE_FILE"
    )
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$config_file"; then
            error "配置文件中缺少变量: $var"
            return 1
        else
            log "✅ 配置变量存在: $var"
        fi
    done
    
    log "✅ 配置文件检查通过"
    return 0
}

# 测试离线包目录
test_offline_packages() {
    log "🔍 测试离线包目录"
    
    local pkg_dir="$SCRIPT_DIR/softs/postgresql17-offline-complete-pkgs"
    
    if [ ! -d "$pkg_dir" ]; then
        error "离线包目录不存在: $pkg_dir"
        return 1
    fi
    
    # 检查关键包是否存在
    local key_packages=(
        "postgresql-17_"
        "postgresql-client-17_"
        "postgresql-common_"
        "libpq5_"
    )
    
    for pkg_pattern in "${key_packages[@]}"; do
        if ls "$pkg_dir"/${pkg_pattern}*.deb 1> /dev/null 2>&1; then
            log "✅ 找到关键包: $pkg_pattern*.deb"
        else
            warn "未找到关键包: $pkg_pattern*.deb"
        fi
    done
    
    local total_debs=$(ls "$pkg_dir"/*.deb 2>/dev/null | wc -l)
    log "📦 离线包总数: $total_debs"
    
    if [ "$total_debs" -gt 0 ]; then
        log "✅ 离线包目录检查通过"
        return 0
    else
        error "离线包目录为空"
        return 1
    fi
}

# 主测试函数
main() {
    echo -e "${BLUE}"
    echo "========================================"
    echo "  PostgreSQL 17 脚本测试工具"
    echo "  版本: 1.0.0"
    echo "========================================"
    echo -e "${NC}"
    
    local test_passed=0
    local test_failed=0
    
    # 测试脚本列表
    local scripts=(
        "$SCRIPT_DIR/scripts/00__config.sh"
        "$SCRIPT_DIR/scripts/12__postgresql_deb_pkg.sh"
        "$SCRIPT_DIR/scripts/13__postgresql_installer.sh"
        "$SCRIPT_DIR/scripts/unist/unist_postgresql.sh"
        "$SCRIPT_DIR/uninstall_postgresql.sh"
    )
    
    # 测试配置文件
    if test_config_file; then
        ((test_passed++))
    else
        ((test_failed++))
    fi
    
    # 测试离线包
    if test_offline_packages; then
        ((test_passed++))
    else
        ((test_failed++))
    fi
    
    # 测试每个脚本
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            if test_script_syntax "$script" && test_script_dependencies "$script"; then
                ((test_passed++))
            else
                ((test_failed++))
            fi
        else
            warn "脚本文件不存在: $(basename "$script")"
            ((test_failed++))
        fi
    done
    
    # 显示测试结果
    echo ""
    echo "========================================"
    echo -e "测试结果："
    echo -e "  ${GREEN}✅ 通过: $test_passed${NC}"
    echo -e "  ${RED}❌ 失败: $test_failed${NC}"
    echo "========================================"
    
    if [ $test_failed -eq 0 ]; then
        log "🎉 所有测试通过！脚本可以使用。"
        echo ""
        echo "使用方法："
        echo "1. 安装 PostgreSQL 17："
        echo "   sudo bash scripts/13__postgresql_installer.sh"
        echo ""
        echo "2. 卸载 PostgreSQL 17："
        echo "   sudo bash uninstall_postgresql.sh"
        echo "   或"
        echo "   sudo bash scripts/unist/unist_postgresql.sh"
        return 0
    else
        error "存在测试失败，请修复后再使用脚本"
        return 1
    fi
}

# 执行测试
main "$@"
