{"appliance_id": "7bfa7a66-b1fa-4e5e-9b85-95d74440ee31", "name": "Checkpoint GAiA", "category": "firewall", "description": "Check Point Gaia is the next generation Secure Operating System for all Check Point Appliances, Open Servers and Virtualized Gateways.\n\nGaia combines the best features from IPSO and SecurePlatform (SPLAT) into a single unified OS providing greater efficiency and robust performance. By upgrading to Gaia, customers will benefit from improved appliance connection capacity and reduced operating costs. With Gaia, IP Appliance customers will gain the ability to leverage the full breadth and power of all Check Point Software Blades.\n\nGaia secures IPv6 networks utilizing the Check Point Acceleration & Clustering technology and it protects the most dynamic network and virtualized environments by supporting 5 different dynamic routing protocols. As a 64-Bit OS, Gaia increases the connection capacity of existing appliances supporting up-to 10M concurrent connections for select 2012 Models.\n\nGaia simplifies management with segregation of duties by enabling role-based administrative access. Furthermore, Gaia greatly increases operation efficiency by offering Automatic Software Update.\n\nThe feature-rich Web interface allows for search of any command or property in a second.\n\nGaia provides backward compatibility with IPSO and SPLAT CLI-style commands making it an easy transition for existing Check Point customers.", "vendor_name": "Checkpoint", "vendor_url": "https://www.checkpoint.com", "documentation_url": "http://downloads.checkpoint.com/dc/download.htm?ID=26770", "product_name": "Gaia", "registry_version": 4, "status": "experimental", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "usage": "At boot choose the install on disk options. You need to open quickly the terminal after launching the appliance if you want to see the menu. You need a web browser in order to finalize the installation. You can use the firefox appliance for this.", "qemu": {"adapter_type": "e1000", "adapters": 8, "ram": 4096, "cpus": 2, "hda_disk_interface": "ide", "arch": "x86_64", "console_type": "telnet", "boot_priority": "dc", "kvm": "require", "process_priority": "normal"}, "images": [{"filename": "Check_Point_R80.10_T479_Gaia.iso", "version": "80.10", "md5sum": "1b97cce21dbee78fec505b44e637cc9a", "filesize": 3301212160, "download_url": "https://supportcenter.checkpoint.com/supportcenter/portal/user/anon/page/default.psml/media-type/html?action=portlets.DCFileAction&eventSubmit_doGetdcdetails=&fileid=54509"}, {"filename": "Check_Point_R77.30_Install_and_Upgrade_T5.Gaia.iso", "version": "77.30", "md5sum": "3f6f459df3fb3beaf7b2457f08982425", "filesize": 289692076, "download_url": "https://supportcenter.checkpoint.com/supportcenter/portal?eventSubmit_doGoviewsolutiondetails=&solutionid=sk104859"}, {"filename": "Check_Point_R77.20_T124_Install.Gaia.iso", "version": "77.20", "md5sum": "7552fa2ad3e1f0ac31615b60b736969c", "filesize": 2632974336, "download_url": "https://supportcenter.checkpoint.com/supportcenter/portal?eventSubmit_doGoviewsolutiondetails=&solutionid=sk104859"}, {"filename": "empty100G.qcow2", "version": "1.0", "md5sum": "1e6409a4523ada212dea2ebc50e50a65", "filesize": 197120, "download_url": "https://sourceforge.net/projects/gns-3/files/Empty%20Qemu%20disk/", "direct_download_url": "https://sourceforge.net/projects/gns-3/files/Empty%20Qemu%20disk/empty100G.qcow2/download"}], "versions": [{"name": "80.10", "images": {"hda_disk_image": "empty100G.qcow2", "cdrom_image": "Check_Point_R80.10_T479_Gaia.iso"}}, {"name": "77.30", "images": {"hda_disk_image": "empty100G.qcow2", "cdrom_image": "Check_Point_R77.30_Install_and_Upgrade_T5.Gaia.iso"}}, {"name": "77.20", "images": {"hda_disk_image": "empty100G.qcow2", "cdrom_image": "Check_Point_R77.20_T124_Install.Gaia.iso"}}]}