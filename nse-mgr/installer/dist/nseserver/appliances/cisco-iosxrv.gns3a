{"appliance_id": "78fc5177-c399-4369-a6f8-e9c9b217c2e2", "name": "Cisco IOS XRv", "category": "router", "description": "IOS XRv supports the control plane features introduced in Cisco IOS XR.", "vendor_name": "Cisco", "vendor_url": "http://www.cisco.com/", "documentation_url": "https://developer.cisco.com/docs/modeling-labs/ios-xrv/", "product_name": "IOS XRv", "product_url": "https://developer.cisco.com/modeling-labs/", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "usage": "You can set admin username and password on first boot. Don't forget about the two-staged configuration, you have to commit your changes.", "first_port_name": "MgmtEth0/0/CPU0/0", "port_name_format": "Gi0/0/0/{0}", "qemu": {"adapter_type": "e1000", "adapters": 9, "ram": 3072, "hda_disk_interface": "ide", "arch": "i386", "console_type": "telnet", "kvm": "require"}, "images": [{"filename": "iosxrv-k9-demo-6.1.3.qcow2", "version": "6.1.3", "md5sum": "1693b5d22a398587dd0fed2877d8dfac", "filesize": *********, "download_url": "https://learningnetworkstore.cisco.com/myaccount"}, {"filename": "iosxrv-k9-demo-6.0.1.qcow2", "version": "6.0.1", "md5sum": "0831ecf43628eccb752ebb275de9a62a", "filesize": *********, "download_url": "https://learningnetworkstore.cisco.com/myaccount"}], "versions": [{"name": "6.1.3", "images": {"hda_disk_image": "iosxrv-k9-demo-6.1.3.qcow2"}}, {"name": "6.0.1", "images": {"hda_disk_image": "iosxrv-k9-demo-6.0.1.qcow2"}}]}