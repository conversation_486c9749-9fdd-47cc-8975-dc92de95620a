{"appliance_id": "a9dc537f-3093-42c2-95d9-fca800e59f82", "name": "Web Security Virtual Appliance", "category": "firewall", "description": "The Cisco WSA was one of the first secure web gateways to combine leading protections to help organizations address the growing challenges of securing and controlling web traffic. It enables simpler, faster deployment with fewer maintenance requirements, reduced latency, and lower operating costs. \"Set and forget\" technology frees staff after initial automated policy settings go live, and automatic security updates are pushed to network devices every 3 to 5 minutes. Flexible deployment options and integration with your existing security infrastructure help you meet quickly evolving security requirements.", "vendor_name": "Cisco", "vendor_url": "http://www.cisco.com/", "documentation_url": "http://www.cisco.com/c/en/us/support/security/web-security-appliance/tsd-products-support-series-home.html", "product_name": "Web Security Virtual Appliance", "product_url": "http://www.cisco.com/c/en/us/products/security/web-security-appliance/index.html", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "usage": "Boot takes some time. NIC0 is the management port, it gets its initial address using DHCP. Default credentials: admin / ironport", "port_name_format": "nic{0}", "qemu": {"adapter_type": "virtio-net-pci", "adapters": 5, "ram": 4096, "hda_disk_interface": "virtio", "arch": "x86_64", "console_type": "telnet", "boot_priority": "c", "kvm": "require"}, "images": [{"filename": "coeus-9-0-1-162-S000V.qcow2", "version": "9.0.1", "md5sum": "3561a6dd9e1b0481e6e68f7e0235fa9b", "filesize": 4753719296, "download_url": "https://software.cisco.com/download/release.html?mdfid=284806698&flowid=41610&softwareid=282975114&release=9.0.1&relind=AVAILABLE&rellifecycle=LD&reltype=latest"}], "versions": [{"name": "9.0.1", "images": {"hda_disk_image": "coeus-9-0-1-162-S000V.qcow2"}}]}