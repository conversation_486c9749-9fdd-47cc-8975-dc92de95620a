{"appliance_id": "f049539d-db46-422f-8d10-ff9b96a2a4ae", "name": "DNS", "category": "guest", "description": "This appliance provides DNS using dnsmasq with the local domain set to \"lab\".", "vendor_name": "Ubuntu", "vendor_url": "https://www.ubuntu.com/", "product_name": "DNS", "registry_version": 4, "status": "stable", "maintainer": "<PERSON><PERSON>", "maintainer_email": "<EMAIL>", "usage": "You can add records by adding entries to the /etc/hosts file in the following format:\n%IP_ADDRESS% %HOSTNAME%.lab %HOSTNAME%\n\nExample:\n192.168.123.10 router1.lab router1\n\nIf you require DNS requests to be serviced from a different subnet than the one that the DNS server resides on then do the following:\n\n1. Edit (nano or vim) /ect/init.d/dnsmasq\n2. Find the line DNSMASQ_OPTS=\"$DNSMASQ_OPTS --local-service\"\n3. Remove the --local-service or comment that line out and add DNSMASQ_OPTS=\"\"\n4. Restart dnsmasq using the following command: service dnsmasq restart \n\n There is an error in this Docker container, edit /etc/dnsmasq.conf, add: interface=eth0", "symbol": "linux_guest.svg", "docker": {"adapters": 1, "image": "adosztal/dns:latest", "console_type": "telnet"}}