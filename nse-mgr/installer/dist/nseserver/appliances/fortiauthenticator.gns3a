{"appliance_id": "57e289b6-68a4-4911-a916-0d3def89ef74", "name": "FortiAuthenticator", "category": "guest", "description": "FortiAuthenticator user identity management appliances strengthen enterprise security by simplifying and centralizing the management and storage of user identity information.", "vendor_name": "Fortinet", "vendor_url": "http://www.fortinet.com/", "vendor_logo_url": "https://raw.githubusercontent.com/GNS3/gns3-registry/master/vendor-logos/FortiAuthenticator.jpg", "documentation_url": "http://docs.fortinet.com/fortiauthenticator/admin-guides", "product_name": "FortiAuthenticator", "product_url": "https://www.fortinet.com/products/identity-access-management/fortiauthenticator.html", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "usage": "Default username is admin, no password is set. First boot takes longer.", "symbol": "fortinet.svg", "port_name_format": "Port{port1}", "qemu": {"adapter_type": "e1000", "adapters": 4, "ram": 4096, "hda_disk_interface": "virtio", "hdb_disk_interface": "virtio", "arch": "x86_64", "console_type": "telnet", "boot_priority": "c", "kvm": "allow"}, "images": [{"filename": "FAC_VM_KVM-v6.6.1-build1660-FORTINET.out.kvm_fackvm.qcow2", "version": "6.6.1", "md5sum": "4b2b475ac8b6f88b5033dca367d53cbb", "filesize": 138477584, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v6.5.5-build1385-FORTINET.out.kvm_fackvm.qcow2", "version": "6.5.5", "md5sum": "6850128ac51cee2577114ecd487786ff", "filesize": 112918544, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v6.4.9-build1067-FORTINET.out.kvm_fackvm.qcow2", "version": "6.4.9", "md5sum": "aee068a16fb2ca332d41e6add499b7d3", "filesize": 112197648, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v6-build0058-FORTINET.out.kvm.qcow2", "version": "6.0.3", "md5sum": "5812e4ab63aa1feba85324897dd37fa5", "filesize": 73912320, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v6-build0010-FORTINET.out.kvm.qcow2", "version": "6.0.0", "md5sum": "6699c0b24e54dc6a5215ba3945abb364", "filesize": 74317824, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v5-build0366-FORTINET.out.kvm.qcow2", "version": "5.5.0", "md5sum": "c6ae060f8840b467efac6701a7e8e6af", "filesize": 72982528, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v5-build0297-FORTINET.out.kvm.qcow2", "version": "5.4.1", "md5sum": "e63bcaae040f903f9de315f582702619", "filesize": 72617984, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v5-build0163-FORTINET.out.kvm.qcow2", "version": "5.2.2", "md5sum": "e746b3c6c29b0356ec6b90ed14d4b098", "filesize": 71565312, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v500-build0161-FORTINET.out.kvm.qcow2", "version": "5.2.1", "md5sum": "c46ad2de4a20296ebd35b3fcc2d81a1d", "filesize": 72536064, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v5-build0155-FORTINET.out.kvm.qcow2", "version": "5.2.0", "md5sum": "69b55ce7c8094ccd736bbfe8a3262b31", "filesize": 71782400, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v500-build0091-FORTINET.out.kvm.qcow2", "version": "5.1.2", "md5sum": "7bdafd32db552954c4c7fe60296fc600", "filesize": 71135232, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v500-build0086-FORTINET.out.kvm.qcow2", "version": "5.1.1", "md5sum": "960017582fe16e7ce7ab9602600e65fe", "filesize": 71819264, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v500-build0083-FORTINET.out.kvm.qcow2", "version": "5.1.0", "md5sum": "eec53c2dbe5d00c8ce2a7ca50226325a", "filesize": 72495104, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v500-build0012-FORTINET.out.kvm.qcow2", "version": "5.0.0", "md5sum": "2af90bdad68a37f38fda39ee04cf2fba", "filesize": 62771200, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v6.6.1-build1660-FORTINET.out.kvm_datadrive.qcow2", "version": "6.6.1", "md5sum": "9bbaa1ce1508b4af1f43ba00879269f9", "filesize": 197568, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-v6.5.5-build1385-FORTINET.out.kvm_datadrive.qcow2", "version": "6.4.x, 6.5.x", "md5sum": "3f7173307047cf562f55ed2f99450c10", "filesize": 197568, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}, {"filename": "FAC_VM_KVM-ALL-DATADRIVE.qcow2", "version": "All", "md5sum": "09bad6cfe6301930adbc829eb8a67149", "filesize": 258048, "download_url": "https://support.fortinet.com/Download/FirmwareImages.aspx"}], "versions": [{"name": "6.6.1", "images": {"hda_disk_image": "FAC_VM_KVM-v6.6.1-build1660-FORTINET.out.kvm_fackvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-v6.6.1-build1660-FORTINET.out.kvm_datadrive.qcow2"}}, {"name": "6.5.5", "images": {"hda_disk_image": "FAC_VM_KVM-v6.5.5-build1385-FORTINET.out.kvm_fackvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-v6.5.5-build1385-FORTINET.out.kvm_datadrive.qcow2"}}, {"name": "6.4.9", "images": {"hda_disk_image": "FAC_VM_KVM-v6.4.9-build1067-FORTINET.out.kvm_fackvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-v6.5.5-build1385-FORTINET.out.kvm_datadrive.qcow2"}}, {"name": "6.0.3", "images": {"hda_disk_image": "FAC_VM_KVM-v6-build0058-FORTINET.out.kvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-ALL-DATADRIVE.qcow2"}}, {"name": "6.0.0", "images": {"hda_disk_image": "FAC_VM_KVM-v6-build0010-FORTINET.out.kvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-ALL-DATADRIVE.qcow2"}}, {"name": "5.5.0", "images": {"hda_disk_image": "FAC_VM_KVM-v5-build0366-FORTINET.out.kvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-ALL-DATADRIVE.qcow2"}}, {"name": "5.4.1", "images": {"hda_disk_image": "FAC_VM_KVM-v5-build0297-FORTINET.out.kvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-ALL-DATADRIVE.qcow2"}}, {"name": "5.2.2", "images": {"hda_disk_image": "FAC_VM_KVM-v5-build0163-FORTINET.out.kvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-ALL-DATADRIVE.qcow2"}}, {"name": "5.2.1", "images": {"hda_disk_image": "FAC_VM_KVM-v500-build0161-FORTINET.out.kvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-ALL-DATADRIVE.qcow2"}}, {"name": "5.2.0", "images": {"hda_disk_image": "FAC_VM_KVM-v5-build0155-FORTINET.out.kvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-ALL-DATADRIVE.qcow2"}}, {"name": "5.1.2", "images": {"hda_disk_image": "FAC_VM_KVM-v500-build0091-FORTINET.out.kvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-ALL-DATADRIVE.qcow2"}}, {"name": "5.1.1", "images": {"hda_disk_image": "FAC_VM_KVM-v500-build0086-FORTINET.out.kvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-ALL-DATADRIVE.qcow2"}}, {"name": "5.1.0", "images": {"hda_disk_image": "FAC_VM_KVM-v500-build0083-FORTINET.out.kvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-ALL-DATADRIVE.qcow2"}}, {"name": "5.0.0", "images": {"hda_disk_image": "FAC_VM_KVM-v500-build0012-FORTINET.out.kvm.qcow2", "hdb_disk_image": "FAC_VM_KVM-ALL-DATADRIVE.qcow2"}}]}