{"appliance_id": "82b0804c-1ded-44d4-a79e-608f3fb505bd", "name": "Juniper vMX", "category": "router", "description": "The vMX is a full-featured, carrier-grade virtual MX Series 3D Universal Edge Router that extends 15+ years of Juniper Networks edge routing expertise to the virtual realm. This appliance is a single VM pre-release version that does not require to be paired with another VM like in the vCP/vFP architecture.", "vendor_name": "Juniper", "vendor_url": "https://www.juniper.net/us/en/", "documentation_url": "http://www.juniper.net/techpubs/", "product_name": "Juniper vMX", "product_url": "http://www.juniper.net/us/en/products-services/routing/mx-series/vmx/", "registry_version": 6, "status": "experimental", "maintainer": "none", "maintainer_email": "<EMAIL>", "usage": "Initial username is root, no password.\n\nTIPS:\n* Use the 'show chassis fpc pic-status' command to check that PIC 0 is online and shows 'Virtual 10x1GE PIC'\n* Use the second adapter (em2) and above to connect to other nodes (the first two interfaces are for internal purposes).", "symbol": "juniper-vmx.svg", "first_port_name": "fxp0", "port_name_format": "em{port1}", "qemu": {"adapter_type": "virtio-net-pci", "adapters": 12, "custom_adapters": [{"adapter_number": 0, "adapter_type": "e1000"}, {"adapter_number": 1, "adapter_type": "e1000"}], "ram": 1024, "hda_disk_interface": "ide", "arch": "x86_64", "console_type": "telnet", "kvm": "require", "options": "-nographic -machine q35,smbios-entry-point-type=32"}, "images": [{"filename": "jinstall-vmx-14.1R4.8-domestic.img", "version": "14.1R4.8", "md5sum": "85aa3048e8648bf91e893455645cad03", "filesize": 681377792}], "versions": [{"name": "14.1R4.8", "images": {"hda_disk_image": "jinstall-vmx-14.1R4.8-domestic.img"}}]}