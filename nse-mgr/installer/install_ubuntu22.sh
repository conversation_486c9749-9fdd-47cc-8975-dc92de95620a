#!/bin/bash

# ===================================================================
# NSE 一键部署脚本 - Ubuntu 22.04 LTS
# 功能：创建PostgreSQL数据库、部署NSE服务、配置系统服务
# ===================================================================

set -euo pipefail

# ==================== 全局配置 ====================
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/logs/nse-deploy-$(date +%Y%m%d-%H%M%S).log"

# 项目配置
NSE_LAUNCHER_JAR="nse-mgr-launcher-1.0.0-SNAPSHOT.jar"
NSESERVER_ZIP="nseserver.zip"
DIST_DIR="$SCRIPT_DIR/dist"

# 安装目录
NSE_INSTALL_PROJECT_DIR="/data/ruijie/nse"

# 数据库配置
DB_USER="sys_nse_rw"
DB_PASS="Ruijie@NSE20250902!!!"
DB_NAME="nse"
DB_PORT=5432
PG_SERVICE_USER="postgres"

# NSE 环境变量键名
CPU_CORES_KEY="NSE_INIT_CPU_CORES"
MAX_MEMORY_KEY="NSE_INIT_MAX_MEMORY"
MAX_DISK_KEY="NSE_INIT_MAX_DISK"

# 默认配置值
DEFAULT_CONCURRENCY_NODES=10
DEFAULT_CPU_CORES_PER_NODE=2
DEFAULT_MEMORY_PER_NODE_GB=4
DEFAULT_DISK_PER_NODE_GB=20

# ==================== 工具函数 ====================
log() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo "$message"
    echo "$message" >> "$LOG_FILE"
}

error_exit() {
    log "❌ 错误: $1"
    exit 1
}

success() {
    log "✅ $1"
}

warning() {
    log "⚠️  $1"
}

# 创建日志目录
mkdir -p "$(dirname "$LOG_FILE")"

# ==================== 系统检查 ====================
check_system() {
    log "🔍 检查系统环境..."

    # 检查Ubuntu版本
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        if [[ "$VERSION_ID" == "22.04" ]]; then
            success "检测到 Ubuntu 22.04 LTS"
        else
            warning "当前系统版本为 $VERSION_ID，脚本针对 Ubuntu 22.04 优化"
        fi
    fi

    # 检查root权限
    if [ "$EUID" -ne 0 ]; then
        error_exit "请使用 sudo 运行此脚本"
    fi

    # 检查PostgreSQL服务
    if ! systemctl is-active --quiet postgresql; then
        error_exit "PostgreSQL 服务未运行，请先启动 PostgreSQL"
    fi
    success "PostgreSQL 服务运行正常"

    # 检查jar包
    if [ ! -f "$DIST_DIR/$NSE_LAUNCHER_JAR" ]; then
        error_exit "未找到 $NSE_LAUNCHER_JAR 文件"
    fi
    success "NSE jar包检查通过"
}

# ==================== 创建PostgreSQL数据库 ====================
create_database() {
    log "🗄️  创建PostgreSQL数据库..."

    # 检查用户是否已存在
    if sudo -u "$PG_SERVICE_USER" psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='$DB_USER'" | grep -q 1; then
        log "用户 '$DB_USER' 已存在，跳过创建"
    else
        sudo -u "$PG_SERVICE_USER" psql -c "CREATE USER \"$DB_USER\" WITH ENCRYPTED PASSWORD '$DB_PASS';" || error_exit "创建用户失败"
        success "用户 '$DB_USER' 创建成功"
    fi

    # 检查数据库是否已存在
    if sudo -u "$PG_SERVICE_USER" psql -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
        log "数据库 '$DB_NAME' 已存在，跳过创建"
    else
        sudo -u "$PG_SERVICE_USER" psql -c "CREATE DATABASE \"$DB_NAME\" OWNER \"$DB_USER\";" || error_exit "创建数据库失败"
        success "数据库 '$DB_NAME' 创建成功"
    fi

    # 授予权限
    sudo -u "$PG_SERVICE_USER" psql -d "$DB_NAME" -c "GRANT ALL PRIVILEGES ON DATABASE \"$DB_NAME\" TO \"$DB_USER\";"
    sudo -u "$PG_SERVICE_USER" psql -d "$DB_NAME" -c "GRANT ALL ON SCHEMA public TO \"$DB_USER\";"
    sudo -u "$PG_SERVICE_USER" psql -d "$DB_NAME" -c "ALTER USER \"$DB_USER\" CREATEDB;"
    success "数据库权限配置完成"

    # 初始化数据库结构
    local sql_file="$SCRIPT_DIR/../db/V1.0.0_init_db.sql"
    if [ -f "$sql_file" ]; then
        log "正在初始化数据库结构..."
        sudo -u "$PG_SERVICE_USER" psql -d "$DB_NAME" -f "$sql_file" || warning "数据库初始化脚本执行失败"
        success "数据库结构初始化完成"
    else
        warning "未找到数据库初始化脚本: $sql_file"
    fi
}

# ==================== 用户配置输入 ====================
collect_user_config() {
    log "🔧 NSE 系统配置向导"
    echo ""
    echo "请根据您的服务器资源和业务需求配置以下参数："
    echo ""

    # 并发节点数
    while true; do
        echo -n "📊 请输入并发节点数 (默认: $DEFAULT_CONCURRENCY_NODES): "
        read -r input
        if [ -z "$input" ]; then
            CONCURRENCY_NODES=$DEFAULT_CONCURRENCY_NODES
            break
        elif [[ "$input" =~ ^[1-9][0-9]*$ ]]; then
            CONCURRENCY_NODES=$input
            break
        else
            echo "❌ 请输入正整数"
        fi
    done

    # CPU核心数
    while true; do
        echo -n "🖥️  请输入每个并发节点占用的CPU核心数 (默认: $DEFAULT_CPU_CORES_PER_NODE): "
        read -r input
        if [ -z "$input" ]; then
            CPU_CORES_PER_NODE=$DEFAULT_CPU_CORES_PER_NODE
            break
        elif [[ "$input" =~ ^[1-9][0-9]*$ ]]; then
            CPU_CORES_PER_NODE=$input
            break
        else
            echo "❌ 请输入正整数"
        fi
    done

    # 内存大小
    while true; do
        echo -n "💾 请输入每个并发节点占用的内存大小(GB) (默认: $DEFAULT_MEMORY_PER_NODE_GB): "
        read -r input
        if [ -z "$input" ]; then
            MEMORY_PER_NODE_GB=$DEFAULT_MEMORY_PER_NODE_GB
            break
        elif [[ "$input" =~ ^[0-9]+(\.[0-9]+)?$ ]] && (( $(echo "$input > 0" | bc -l) )); then
            MEMORY_PER_NODE_GB=$input
            break
        else
            echo "❌ 请输入正数"
        fi
    done

    # 磁盘空间
    while true; do
        echo -n "💿 请输入每个并发节点占用的磁盘空间(GB) (默认: $DEFAULT_DISK_PER_NODE_GB): "
        read -r input
        if [ -z "$input" ]; then
            DISK_PER_NODE_GB=$DEFAULT_DISK_PER_NODE_GB
            break
        elif [[ "$input" =~ ^[0-9]+(\.[0-9]+)?$ ]] && (( $(echo "$input > 0" | bc -l) )); then
            DISK_PER_NODE_GB=$input
            break
        else
            echo "❌ 请输入正数"
        fi
    done

    # 配置确认
    echo ""
    log "📋 配置确认"
    echo "=================================================="
    echo "并发节点数:           $CONCURRENCY_NODES"
    echo "每节点CPU核心数:      $CPU_CORES_PER_NODE"
    echo "每节点内存大小:       ${MEMORY_PER_NODE_GB}GB"
    echo "每节点磁盘空间:       ${DISK_PER_NODE_GB}GB"
    echo "=================================================="
    echo "总计资源需求:"
    echo "CPU核心数:           $((CONCURRENCY_NODES * CPU_CORES_PER_NODE))"
    echo "内存大小:            $(echo "$CONCURRENCY_NODES * $MEMORY_PER_NODE_GB" | bc)GB"
    echo "磁盘空间:            $(echo "$CONCURRENCY_NODES * $DISK_PER_NODE_GB" | bc)GB"
    echo "=================================================="
    echo ""

    while true; do
        echo -n "✅ 确认以上配置正确吗？(y/n): "
        read -r -n 1 confirm
        echo ""
        case $confirm in
            [Yy]* ) success "配置已确认"; break;;
            [Nn]* ) log "请重新运行脚本进行配置"; exit 0;;
            * ) echo "请输入 y 或 n";;
        esac
    done
}

# ==================== 设置环境变量 ====================
setup_environment() {
    log "🌍 设置系统环境变量..."

    # 创建环境变量文件
    local env_file="/etc/environment"
    local temp_file=$(mktemp)

    # 备份原文件
    cp "$env_file" "$env_file.bak.$(date +%Y%m%d-%H%M%S)"

    # 处理环境变量
    local vars_to_set=(
        "$CPU_CORES_KEY=$CPU_CORES_PER_NODE"
        "$MAX_MEMORY_KEY=$MEMORY_PER_NODE_GB"
        "$MAX_DISK_KEY=$DISK_PER_NODE_GB"
    )

    # 读取原文件，替换或保留
    while IFS= read -r line; do
        local skip_line=false
        for var in "${vars_to_set[@]}"; do
            local key="${var%%=*}"
            if [[ "$line" =~ ^export[[:space:]]+$key= ]] || [[ "$line" =~ ^$key= ]]; then
                skip_line=true
                break
            fi
        done
        if [ "$skip_line" = false ]; then
            echo "$line" >> "$temp_file"
        fi
    done < "$env_file"

    # 添加新的环境变量
    for var in "${vars_to_set[@]}"; do
        echo "export $var" >> "$temp_file"
    done

    # 写回文件
    cp "$temp_file" "$env_file"
    chmod 644 "$env_file"
    rm -f "$temp_file"

    # 设置当前会话的环境变量
    export NSE_INIT_CPU_CORES="$CPU_CORES_PER_NODE"
    export NSE_INIT_MAX_MEMORY="$MEMORY_PER_NODE_GB"
    export NSE_INIT_MAX_DISK="$DISK_PER_NODE_GB"

    success "环境变量设置完成"
    log "   $CPU_CORES_KEY=$CPU_CORES_PER_NODE"
    log "   $MAX_MEMORY_KEY=$MEMORY_PER_NODE_GB"
    log "   $MAX_DISK_KEY=$DISK_PER_NODE_GB"
}

# ==================== 部署NSE服务 ====================
deploy_nse_services() {
    log "🚀 部署NSE服务..."

    sudo mkdir -p "$NSE_INSTALL_PROJECT_DIR/projects"
    sudo mkdir -p "$NSE_INSTALL_PROJECT_DIR/config"

    # 复制jar包
    cp "$DIST_DIR/$NSE_LAUNCHER_JAR" "$NSE_INSTALL_PROJECT_DIR/projects/" || error_exit "复制 launcher jar 失败"
    cp "$DIST_DIR/$NSESERVER_ZIP" "$NSE_INSTALL_PROJECT_DIR/projects/" || error_exit "复制 nseserver.zip 失败"
    sudo unzip "$NSE_INSTALL_PROJECT_DIR/projects/$NSESERVER_ZIP" -d "$NSE_INSTALL_PROJECT_DIR/projects/"
    success "NSE jar包复制完成"

    # 创建配置文件
    cat > "$NSE_INSTALL_PROJECT_DIR/config/nse-global.conf" << EOF
# NSE 全局配置文件
# 生成时间: $(date)

# 数据库配置
DB_HOST=localhost
DB_PORT=$DB_PORT
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASS

# NSE 动态配置
$CPU_CORES_KEY=$CPU_CORES_PER_NODE
$MAX_MEMORY_KEY=$MEMORY_PER_NODE_GB
$MAX_DISK_KEY=$DISK_PER_NODE_GB

# 服务配置
NSE_MGR_PORT=8080
EOF

    chmod 600 "$NSE_INSTALL_PROJECT_DIR/config/nse-global.conf"
    success "NSE配置文件创建完成"
}

# ==================== 创建系统服务 ====================
create_system_services() {
    log "⚙️  创建系统服务..."

    # 检查Java路径
    local java_path
    if command -v java >/dev/null 2>&1; then
        java_path=$(which java)
    elif [ -f "/opt/jdk-21.0.8/bin/java" ]; then
        java_path="/opt/jdk-21.0.8/bin/java"
    elif [ -f "/usr/bin/java" ]; then
        java_path="/usr/bin/java"
    else
        error_exit "未找到Java运行环境"
    fi
    success "Java路径: $java_path"

    # 创建 nse-launcher 服务
    cat > /etc/systemd/system/nse.service << EOF
[Unit]
Description=NSE Launcher Service
After=postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=root
WorkingDirectory=$NSE_INSTALL_PROJECT_DIR
EnvironmentFile=$NSE_INSTALL_PROJECT_DIR/config/nse-global.conf
ExecStart=$java_path -jar $NSE_INSTALL_PROJECT_DIR/projects/$NSE_LAUNCHER_JAR
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    # 重新加载systemd配置
    systemctl daemon-reload

    # 启用服务
    systemctl enable nse.service

    success "系统服务创建完成"
}

# ==================== 启动服务 ====================
start_services() {
    log "🔄 启动NSE服务..."
    systemctl start nse.service

    # 等待launcher服务启动
    local count=0
    while [ $count -lt 30 ]; do
        if systemctl is-active --quiet nse.service; then
            success "nse 服务启动成功"
            break
        fi
        log "等待 nse 服务启动... ($((count+1))/30)"
        sleep 5
        count=$((count+1))
    done

    if [ $count -eq 30 ]; then
        error_exit "nse 服务启动超时"
    fi
}

# ==================== 验证部署 ====================
verify_deployment() {
    log "🔍 验证部署结果..."

    # 检查服务状态
    if systemctl is-active --quiet nse.service; then
        success "nse 服务运行正常"
    else
        warning "nse 服务未运行"
    fi

    # 检查端口监听
    if netstat -tlnp 2>/dev/null | grep -q ":8090.*LISTEN" || ss -tlnp 2>/dev/null | grep -q ":8090.*LISTEN"; then
        success "NSE服务正在监听端口 8090"
    else
        warning "NSE服务可能未正确监听端口 8090"
    fi

    # 显示服务状态
    echo ""
    log "📊 服务状态概览:"
    systemctl status nse.service --no-pager -l || true
}

# ==================== 显示部署信息 ====================
show_deployment_info() {
    echo ""
    log "🎉 NSE部署完成！"
    echo ""
    echo "=================================================="
    echo "📋 部署信息摘要"
    echo "=================================================="
    echo "安装目录:     $NSE_INSTALL_PROJECT_DIR"
    echo "配置文件:     $NSE_INSTALL_PROJECT_DIR/config/nse-global.conf"
    echo "日志目录:     $NSE_INSTALL_PROJECT_DIR/logs"
    echo ""
    echo "数据库信息:"
    echo "  主机:       localhost"
    echo "  端口:       $DB_PORT"
    echo "  数据库:     $DB_NAME"
    echo "  用户:       $DB_USER"
    echo ""
    echo "NSE配置:"
    echo "  并发节点数:   $CONCURRENCY_NODES"
    echo "  CPU核心数:    $CPU_CORES_PER_NODE"
    echo "  内存大小:     ${MEMORY_PER_NODE_GB}GB"
    echo "  磁盘空间:     ${DISK_PER_NODE_GB}GB"
    echo ""
    echo "服务管理命令:"
    echo "  查看nse状态: systemctl status nse"
    echo "  重启nse:     systemctl restart nse"
    echo "  查看日志: journalctl -xef"
    echo ""
    echo "Web访问地址: http://localhost:8090"
    echo "=================================================="
    echo ""
    log "部署日志已保存到: $LOG_FILE"
}

# ==================== 主执行流程 ====================
main() {
    log "🚀 开始NSE一键部署 (Ubuntu 22.04)"

    check_system
    create_database
    collect_user_config
    setup_environment
    deploy_nse_services
    create_system_services
    start_services
    verify_deployment
    show_deployment_info

    success "NSE部署完成！"
}

# 执行主函数
main "$@"
