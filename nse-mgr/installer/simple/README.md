# NSE 一键部署脚本使用说明

## 概述

本脚本用于在 Ubuntu 22.04 LTS 系统上一键部署 NSE (Network Simulation Environment) 服务，包括：

- 创建 PostgreSQL 数据库和用户
- 配置 NSE 系统参数（并发节点数、CPU、内存、磁盘）
- 部署 NSE 服务并配置为系统服务
- 自动启动和验证部署结果

## 前置条件

### 系统要求
- Ubuntu 22.04 LTS
- 已安装并运行 PostgreSQL 服务
- 已安装 Java 运行环境 (JDK 8+)
- root 或 sudo 权限

### 文件准备
确保以下文件存在：
```
nse-mgr/installer/simple/
├── install.sh                    # 部署脚本
├── dist/
│   ├── nse-mgr-launcher-1.0.0-SNAPSHOT.jar
│   └── nseserver.zip
└── ../../db/V1.0.0_init_db.sql   # 数据库初始化脚本
```

## 使用方法

### 1. 准备工作

```bash
# 确保PostgreSQL服务运行
sudo systemctl start postgresql
sudo systemctl status postgresql

# 进入脚本目录
cd /path/to/nse-mgr/installer/simple

# 给脚本添加执行权限
chmod +x install.sh
```

### 2. 运行部署脚本

```bash
sudo bash install.sh
```

### 3. 配置参数

脚本会提示您输入以下配置参数：

- **并发节点数** (默认: 10)
  - 系统支持的最大并发仿真节点数量
  
- **每节点CPU核心数** (默认: 2)
  - 每个并发节点占用的CPU核心数
  
- **每节点内存大小(GB)** (默认: 4)
  - 每个并发节点占用的内存大小
  
- **每节点磁盘空间(GB)** (默认: 20)
  - 每个并发节点占用的磁盘空间

### 4. 确认配置

脚本会显示配置摘要和总资源需求，确认无误后输入 `y` 继续。

## 部署内容

### 数据库配置
- 数据库名称: `nse`
- 用户名: `sys_nse_rw`
- 密码: `Ruijie@NSE20250902!!!`
- 端口: `5432`

### 系统服务
- 服务名称: `nse.service`
- 安装目录: `/data/ruijie/nse`
- 配置文件: `/data/ruijie/nse/config/nse-global.conf`
- Web访问: `http://localhost:8090`

### 环境变量
脚本会设置以下系统环境变量供 Spring Boot 应用使用：
- `NSE_INIT_CPU_CORES`: CPU核心数
- `NSE_INIT_MAX_MEMORY`: 内存大小(GB)
- `NSE_INIT_MAX_DISK`: 磁盘空间(GB)

## 服务管理

### 查看服务状态
```bash
systemctl status nse
```

### 启动/停止/重启服务
```bash
systemctl start nse
systemctl stop nse
systemctl restart nse
```

### 查看服务日志
```bash
# 查看实时日志
journalctl -u nse -f

# 查看最近日志
journalctl -u nse -n 100
```

### 禁用/启用开机自启
```bash
systemctl disable nse
systemctl enable nse
```

## 故障排除

### PostgreSQL 连接问题
如果遇到 "unknown user postgres" 错误：

1. 检查 PostgreSQL 安装方式
2. 脚本会自动尝试多种连接方式：
   - `psql -U postgres`
   - `sudo -u postgres psql`
   - `psql` (当前用户)
   - `sudo psql -U postgres`

### 服务启动失败
```bash
# 查看详细错误信息
journalctl -u nse -n 50

# 检查Java环境
java -version

# 检查文件权限
ls -la /data/ruijie/nse/
```

### 端口冲突
如果 8090 端口被占用：
```bash
# 查看端口占用
netstat -tlnp | grep 8090
# 或
ss -tlnp | grep 8090

# 修改配置文件中的端口
sudo nano /data/ruijie/nse/config/nse-global.conf
```

## 卸载

### 停止并删除服务
```bash
sudo systemctl stop nse
sudo systemctl disable nse
sudo rm /etc/systemd/system/nse.service
sudo systemctl daemon-reload
```

### 删除安装目录
```bash
sudo rm -rf /data/ruijie/nse
```

### 删除数据库（可选）
```bash
# 连接PostgreSQL
sudo -u postgres psql

# 删除数据库和用户
DROP DATABASE nse;
DROP USER sys_nse_rw;
\q
```

## 日志文件

部署日志保存在：
```
nse-mgr/installer/simple/nse-deploy-YYYYMMDD-HHMMSS.log
```

## 技术支持

如遇问题，请提供：
1. 系统版本信息 (`lsb_release -a`)
2. PostgreSQL 版本 (`psql --version`)
3. Java 版本 (`java -version`)
4. 部署日志文件
5. 服务日志 (`journalctl -u nse -n 100`)
