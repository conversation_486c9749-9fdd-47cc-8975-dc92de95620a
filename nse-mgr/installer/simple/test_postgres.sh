#!/bin/bash

# PostgreSQL 连接测试脚本
# 用于测试不同的PostgreSQL连接方式

echo "🔍 PostgreSQL 连接测试"
echo "========================"

# 测试方式1: 直接用postgres用户连接
echo "测试方式1: psql -U postgres"
if psql -U postgres -c "SELECT version();" 2>/dev/null; then
    echo "✅ 方式1成功: psql -U postgres"
    WORKING_METHOD="psql -U postgres"
else
    echo "❌ 方式1失败"
fi

echo ""

# 测试方式2: sudo -u postgres
echo "测试方式2: sudo -u postgres psql"
if id postgres >/dev/null 2>&1; then
    echo "postgres用户存在"
    if sudo -u postgres psql -c "SELECT version();" 2>/dev/null; then
        echo "✅ 方式2成功: sudo -u postgres psql"
        WORKING_METHOD="sudo -u postgres psql"
    else
        echo "❌ 方式2失败"
    fi
else
    echo "❌ postgres用户不存在"
fi

echo ""

# 测试方式3: 当前用户
echo "测试方式3: psql (当前用户)"
if psql -c "SELECT version();" 2>/dev/null; then
    echo "✅ 方式3成功: psql"
    WORKING_METHOD="psql"
else
    echo "❌ 方式3失败"
fi

echo ""

# 测试方式4: sudo psql
echo "测试方式4: sudo psql -U postgres"
if sudo psql -U postgres -c "SELECT version();" 2>/dev/null; then
    echo "✅ 方式4成功: sudo psql -U postgres"
    WORKING_METHOD="sudo psql -U postgres"
else
    echo "❌ 方式4失败"
fi

echo ""

# 测试方式5: 指定主机端口
echo "测试方式5: psql -h localhost -p 5432 -U postgres"
if psql -h localhost -p 5432 -U postgres -c "SELECT version();" 2>/dev/null; then
    echo "✅ 方式5成功: psql -h localhost -p 5432 -U postgres"
    WORKING_METHOD="psql -h localhost -p 5432 -U postgres"
else
    echo "❌ 方式5失败"
fi

echo ""
echo "========================"

if [ -n "$WORKING_METHOD" ]; then
    echo "🎉 找到可用的连接方式: $WORKING_METHOD"
    echo ""
    echo "测试创建用户和数据库..."
    
    # 测试创建用户
    if eval "$WORKING_METHOD -c \"SELECT 1 FROM pg_roles WHERE rolname='test_user'\"" | grep -q 1; then
        echo "测试用户已存在，删除后重新创建"
        eval "$WORKING_METHOD -c \"DROP USER test_user;\""
    fi
    
    if eval "$WORKING_METHOD -c \"CREATE USER test_user WITH PASSWORD 'test123';\"; then
        echo "✅ 创建用户成功"
        
        # 测试创建数据库
        if eval "$WORKING_METHOD -c \"SELECT 1 FROM pg_database WHERE datname='test_db'\"" | grep -q 1; then
            echo "测试数据库已存在，删除后重新创建"
            eval "$WORKING_METHOD -c \"DROP DATABASE test_db;\""
        fi
        
        if eval "$WORKING_METHOD -c \"CREATE DATABASE test_db OWNER test_user;\"; then
            echo "✅ 创建数据库成功"
            
            # 清理测试数据
            eval "$WORKING_METHOD -c \"DROP DATABASE test_db;\""
            eval "$WORKING_METHOD -c \"DROP USER test_user;\""
            echo "✅ 清理测试数据完成"
            
            echo ""
            echo "🎉 PostgreSQL连接和权限测试通过！"
            echo "建议使用的连接方式: $WORKING_METHOD"
        else
            echo "❌ 创建数据库失败"
        fi
    else
        echo "❌ 创建用户失败"
    fi
else
    echo "❌ 未找到可用的PostgreSQL连接方式"
    echo ""
    echo "请检查："
    echo "1. PostgreSQL服务是否运行: systemctl status postgresql"
    echo "2. PostgreSQL配置是否正确"
    echo "3. 当前用户是否有足够权限"
    echo ""
    echo "常见解决方案："
    echo "1. 重启PostgreSQL: sudo systemctl restart postgresql"
    echo "2. 检查pg_hba.conf配置"
    echo "3. 创建postgres用户: sudo useradd postgres"
fi

echo ""
echo "系统信息："
echo "当前用户: $(whoami)"
echo "PostgreSQL版本: $(psql --version 2>/dev/null || echo '未安装或不在PATH中')"
echo "PostgreSQL服务状态: $(systemctl is-active postgresql 2>/dev/null || echo '未知')"
